const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testGoogleOAuth() {
  console.log('🧪 Testing Google OAuth Endpoints\n');

  try {
    // 1. Test OAuth2 Status
    console.log('1. Testing OAuth2 Status...');
    const statusResponse = await axios.get(`${BASE_URL}/oauth2/status`);
    console.log('✅ OAuth2 Status:', statusResponse.data);
    console.log('');

    // 2. Test Google Auth Initiation
    console.log('2. Testing Google Auth Initiation...');
    try {
      const authResponse = await axios.get(`${BASE_URL}/auth/google`, {
        maxRedirects: 0,
        validateStatus: (status) => status === 302
      });
      console.log('✅ Google Auth redirect detected');
      console.log('📍 Redirect URL:', authResponse.headers.location);
    } catch (error) {
      if (error.response?.status === 302) {
        console.log('✅ Google Auth redirect detected');
        console.log('📍 Redirect URL:', error.response.headers.location);
      } else {
        console.log('❌ Google Auth initiation failed:', error.message);
      }
    }
    console.log('');

    // 3. Test Swagger Documentation
    console.log('3. Testing Swagger Documentation...');
    try {
      const swaggerResponse = await axios.get(`${BASE_URL}/api-docs`);
      console.log('✅ Swagger documentation is available');
    } catch (error) {
      console.log('❌ Swagger documentation not accessible:', error.message);
    }
    console.log('');

    // 4. Test Available Endpoints
    console.log('4. Available OAuth Endpoints:');
    console.log(`   - Google Auth: ${BASE_URL}/auth/google`);
    console.log(`   - OAuth Callback: ${BASE_URL}/oauth2/callback/google`);
    console.log(`   - OAuth Status: ${BASE_URL}/oauth2/status`);
    console.log(`   - Debug Page: ${BASE_URL}/oauth-debug.html`);
    console.log(`   - Swagger Docs: ${BASE_URL}/api-docs`);
    console.log('');

    // 5. Test Configuration
    console.log('5. Configuration Check:');
    const config = statusResponse.data.googleOAuth;
    console.log(`   - Client ID: ${config.clientId}`);
    console.log(`   - Client Secret: ${config.clientSecret}`);
    console.log(`   - Redirect URI: ${config.redirectUri}`);
    console.log(`   - Scope: ${config.scope}`);
    console.log(`   - Authorized Redirect URIs: ${config.authorizedRedirectUris.join(', ')}`);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testGoogleOAuth(); 