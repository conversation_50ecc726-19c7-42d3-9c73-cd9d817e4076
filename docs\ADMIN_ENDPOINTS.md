# Admin Endpoints Documentation

This document describes the admin functionality implemented in the AI Nest Backend application, following the Spring Boot AdminController pattern but adapted for NestJS.

## Overview

The admin functionality provides secure endpoints for administrative operations, including user management. All admin endpoints require authentication and admin role authorization.

## Authentication & Authorization

### Role-Based Access Control

The application implements a two-tier role system:
- **USER**: Default role for regular users
- **ADMIN**: Administrative role with elevated privileges

### Security Flow

1. **JWT Authentication**: All admin endpoints require a valid JWT token
2. **Role Verification**: The `AdminGuard` checks if the authenticated user has the `ADMIN` role
3. **Database Lookup**: User role is verified against the database for each request

## Admin Endpoints

### GET /api/v1/admin/users

Retrieves a list of all registered users in the system.

#### Security Requirements
- Valid JWT token in Authorization header
- User must have `ADMIN` role

#### Request
```http
GET /api/v1/admin/users
Authorization: Bearer <jwt_token>
```

#### Response

**Success (200 OK)**
```json
{
  "users": [
    {
      "id": "123e4567-e89b-12d3-a456-************",
      "email": "<EMAIL>",
      "name": "John Doe",
      "mobileNumber": "**********",
      "role": "USER",
      "isActive": true,
      "isEmailVerified": true,
      "isAccountLocked": false,
      "socialLoginProvider": "google",
      "createdOn": "2024-01-01T12:00:00Z",
      "updatedOn": "2024-01-01T12:00:00Z",
      "lastLoginAt": "2024-01-01T12:00:00Z"
    }
  ],
  "totalCount": 1,
  "message": "Users retrieved successfully"
}
```

**Error Responses**

- **401 Unauthorized**: Missing or invalid JWT token
- **403 Forbidden**: Valid token but user lacks admin privileges
- **500 Internal Server Error**: Database or service error

## Implementation Details

### Database Schema

The implementation adds a `role` column to the existing `users` table:

```sql
ALTER TABLE users ADD COLUMN role VARCHAR(50) NOT NULL DEFAULT 'USER';
CREATE INDEX idx_users_role ON users(role);
```

### Components

1. **AdminController** (`src/auth/admin.controller.ts`)
   - Handles admin-specific HTTP requests
   - Applies authentication and authorization guards
   - Provides comprehensive API documentation

2. **AdminGuard** (`src/auth/admin.guard.ts`)
   - Implements role-based access control
   - Verifies user has ADMIN role via database lookup
   - Throws appropriate exceptions for unauthorized access

3. **DTOs** (`src/auth/dto/`)
   - `UserResponse`: Standardized user data structure
   - `AdminUserListResponse`: Response wrapper with metadata

4. **Service Methods** (`src/auth/auth.service.ts`)
   - `getAllUsers()`: Retrieves and formats user data
   - Includes logging for audit trails

### Security Considerations

1. **Multi-Layer Security**:
   - JWT authentication at the guard level
   - Role verification via database lookup
   - Method-level authorization decorators

2. **Data Protection**:
   - Sensitive fields (passwords) are excluded from responses
   - User data is properly sanitized and formatted

3. **Audit Logging**:
   - All admin operations are logged with context
   - Includes user identification and operation details

## Testing

The implementation includes comprehensive unit tests:

- **AdminController Tests**: Verify endpoint behavior and error handling
- **AdminGuard Tests**: Test role-based access control logic
- **Integration Tests**: Ensure proper guard and service integration

## Usage Examples

### Creating an Admin User

Update the seed file or manually set a user's role to 'ADMIN':

```sql
UPDATE users SET role = 'ADMIN' WHERE email = '<EMAIL>';
```

### Testing the Endpoint

1. Login as an admin user to get a JWT token
2. Use the token to access admin endpoints:

```bash
curl -H "Authorization: Bearer <jwt_token>" \
     http://localhost:3000/api/v1/admin/users
```

## Future Enhancements

Potential extensions to the admin functionality:

1. **Pagination**: Add pagination support for large user lists
2. **Filtering**: Allow filtering users by role, status, etc.
3. **User Management**: Add endpoints for user creation, updates, and deactivation
4. **Audit Logs**: Implement comprehensive audit logging for all admin actions
5. **Bulk Operations**: Support bulk user operations

## API Documentation

The admin endpoints are fully documented with Swagger/OpenAPI annotations and are available at:
- Development: `http://localhost:3000/api-docs`
- Look for the "Admin" tag in the API documentation
