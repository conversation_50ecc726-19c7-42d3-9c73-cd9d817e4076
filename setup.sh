#!/bin/bash

# AI Nest Backend - Environment Setup Script
# This script helps set up environment files for development

set -e  # Exit on any error

echo "🚀 AI Nest Backend - Environment Setup"
echo "======================================"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if .env.example exists
if [ ! -f ".env.example" ]; then
    print_error ".env.example file not found!"
    echo "Please make sure you're running this script from the project root directory."
    exit 1
fi

print_info "Found .env.example file"

# Create environment files
ENV_FILES=(".env.development" ".env.production" ".env.beta" ".env.test")

echo ""
echo "📁 Creating environment files..."

for env_file in "${ENV_FILES[@]}"; do
    if [ -f "$env_file" ]; then
        print_warning "$env_file already exists. Skipping..."
    else
        cp .env.example "$env_file"
        print_status "Created $env_file"
    fi
done

echo ""
echo "🔧 Environment files created successfully!"
echo ""

# Prompt for development environment setup
echo "🛠️  Development Environment Setup"
echo "================================="
echo ""

read -p "Would you like to configure .env.development now? (y/n): " configure_dev

if [[ $configure_dev =~ ^[Yy]$ ]]; then
    echo ""
    echo "📝 Configuring .env.development..."
    echo "Please provide the following values (press Enter to keep default):"
    echo ""
    
    # Read current values from .env.development
    source .env.development 2>/dev/null || true
    
    # Database configuration
    echo "🗄️  Database Configuration:"
    read -p "Database Host [$DB_HOST]: " new_db_host
    read -p "Database Port [$DB_PORT]: " new_db_port
    read -p "Database User [$DB_USER]: " new_db_user
    read -p "Database Password [$DB_PASSWORD]: " new_db_password
    read -p "Database Name [$DB_NAME]: " new_db_name
    
    echo ""
    echo "🔐 Security Configuration:"
    read -p "JWT Secret (leave empty to generate): " new_jwt_secret
    read -p "Session Secret (leave empty to generate): " new_session_secret
    
    # Generate secrets if not provided
    if [ -z "$new_jwt_secret" ]; then
        new_jwt_secret=$(openssl rand -base64 32 2>/dev/null || echo "your-generated-jwt-secret-$(date +%s)")
        print_info "Generated JWT Secret"
    fi
    
    if [ -z "$new_session_secret" ]; then
        new_session_secret=$(openssl rand -base64 32 2>/dev/null || echo "your-generated-session-secret-$(date +%s)")
        print_info "Generated Session Secret"
    fi
    
    echo ""
    echo "📧 Email Configuration:"
    read -p "SMTP Host [$SMTP_HOST]: " new_smtp_host
    read -p "SMTP Port [$SMTP_PORT]: " new_smtp_port
    read -p "SMTP User [$SMTP_USER]: " new_smtp_user
    read -p "SMTP Password: " new_smtp_pass
    
    echo ""
    echo "🔗 OAuth Configuration:"
    read -p "Google Client ID: " new_google_client_id
    read -p "Google Client Secret: " new_google_client_secret
    
    # Update .env.development file
    {
        echo "# Development Environment Configuration"
        echo "# Generated by setup script on $(date)"
        echo ""
        echo "PORT=3000"
        echo "NODE_ENV=development"
        echo ""
        echo "# Database Configuration"
        echo "DB_HOST=${new_db_host:-$DB_HOST}"
        echo "DB_PORT=${new_db_port:-$DB_PORT}"
        echo "DB_USER=${new_db_user:-$DB_USER}"
        echo "DB_PASSWORD=${new_db_password:-$DB_PASSWORD}"
        echo "DB_NAME=${new_db_name:-$DB_NAME}"
        echo ""
        echo "# Security Configuration"
        echo "JWT_SECRET=$new_jwt_secret"
        echo "JWT_EXPIRATION=86400000"
        echo "SESSION_SECRET=$new_session_secret"
        echo ""
        echo "# Email SMTP Settings"
        echo "SMTP_HOST=${new_smtp_host:-$SMTP_HOST}"
        echo "SMTP_PORT=${new_smtp_port:-$SMTP_PORT}"
        echo "SMTP_USER=${new_smtp_user:-$SMTP_USER}"
        echo "SMTP_PASS=${new_smtp_pass:-$SMTP_PASS}"
        echo "SMTP_FROM=\"\\\"No Reply\\\" <${new_smtp_user:-$SMTP_USER}>\""
        echo ""
        echo "# Application URLs"
        echo "APP_URL=http://localhost:3000"
        echo "API_URL=http://localhost:3000"
        echo ""
        echo "# CORS Configuration"
        echo "CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001"
        echo "CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,PATCH,OPTIONS"
        echo "CORS_ALLOW_CREDENTIALS=true"
        echo "CORS_MAX_AGE=3600"
        echo ""
        echo "# OAuth2 Configuration"
        echo "OAUTH2_AUTHORIZED_REDIRECT_URIS=http://localhost:3000/oauth2/callback/google,http://localhost:3000/oauth2/redirect"
        echo ""
        echo "# Google OAuth Configuration"
        echo "GOOGLE_CLIENT_ID=${new_google_client_id:-your-google-client-id}"
        echo "GOOGLE_CLIENT_SECRET=${new_google_client_secret:-your-google-client-secret}"
        echo "GOOGLE_REDIRECT_URI=http://localhost:3000/oauth2/callback/google"
        echo "GOOGLE_SCOPE=email,profile,openid"
    } > .env.development
    
    print_status ".env.development configured successfully!"
fi

echo ""
echo "📚 Next Steps:"
echo "=============="
echo ""
echo "1. Install dependencies:"
echo "   npm install"
echo ""
echo "2. Set up your database (PostgreSQL):"
echo "   docker-compose up -d  # if using Docker"
echo ""
echo "3. Run database migrations:"
echo "   npm run migration:latest"
echo ""
echo "4. Start the development server:"
echo "   npm start"
echo ""
echo "5. Configure other environment files as needed:"
echo "   - .env.production (for production deployment)"
echo "   - .env.beta (for staging/beta environment)"
echo "   - .env.test (for testing - already configured)"
echo ""

print_warning "Remember to:"
print_warning "- Never commit .env files to Git"
print_warning "- Use strong secrets in production"
print_warning "- Keep your environment files secure"

echo ""
print_status "Setup completed! Happy coding! 🎉"
