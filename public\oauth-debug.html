<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NestJS OAuth2 Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-danger {
            background-color: #dc3545;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .btn-success {
            background-color: #28a745;
        }
        .btn-success:hover {
            background-color: #218838;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
            border: 1px solid #e9ecef;
        }
        .url-display {
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            word-break: break-all;
            margin: 10px 0;
        }
        .config-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .config-item:last-child {
            border-bottom: none;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-ok { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 NestJS OAuth2 Debug Test</h1>
        
        <div class="warning">
            <h3>⚠️ Google OAuth2 Integration Test</h3>
            <p>This debug page will help you test and troubleshoot Google OAuth2 integration in your NestJS application.</p>
        </div>

        <div class="debug-section">
            <h3>🔧 Configuration Check</h3>
            <div id="configCheck">
                <p>Checking configuration...</p>
            </div>
        </div>

        <div class="debug-section">
            <h3>🌐 Test URLs</h3>
            <p><strong>NestJS OAuth2 Endpoints:</strong></p>
            <div class="url-display">http://localhost:3000/auth/google</div>
            <div class="url-display">http://localhost:3000/oauth2/callback/google</div>
            
            <p><strong>Expected Google OAuth2 URL:</strong></p>
            <div class="url-display" id="expectedGoogleUrl">Loading...</div>
            
            <div>
                <a href="/auth/google" class="btn">Test OAuth2 Flow</a>
                <button onclick="testOAuth2Status()" class="btn">Check OAuth2 Status</button>
                <button onclick="testConfigEndpoint()" class="btn">Test Config Endpoint</button>
                <button onclick="showDebugInfo()" class="btn">Show Debug Info</button>
            </div>
        </div>

        <div class="debug-section">
            <h3>📋 Google Console Checklist</h3>
            <div id="checklist">
                <h4>Required Google Console Settings:</h4>
                <ul>
                    <li>
                        <span class="status-indicator status-ok"></span>
                        <strong>Authorized JavaScript origins:</strong>
                        <ul>
                            <li>http://localhost:3000</li>
                            <li>http://localhost:3000</li>
                        </ul>
                    </li>
                    <li>
                        <span class="status-indicator status-ok"></span>
                        <strong>Authorized redirect URIs:</strong>
                        <ul>
                            <li>http://localhost:3000/oauth2/callback/google</li>
                            <li>http://localhost:3000/chidhagni/oauth2/redirect</li>
                        </ul>
                    </li>
                    <li>
                        <span class="status-indicator status-ok"></span>
                        <strong>OAuth consent screen:</strong> Configured and published
                    </li>
                    <li>
                        <span class="status-indicator status-ok"></span>
                        <strong>Test users:</strong> Your email added if in testing mode
                    </li>
                </ul>
            </div>
        </div>

        <div class="debug-section">
            <h3>🔍 Debug Information</h3>
            <div id="debugInfo">
                <p>Click "Show Debug Info" to see detailed information.</p>
            </div>
        </div>

        <div class="debug-section">
            <h3>📊 Current URL Analysis</h3>
            <div id="urlAnalysis">
                <p>Analyzing current URL...</p>
            </div>
        </div>

        <div class="debug-section">
            <h3>🛠️ Troubleshooting Steps</h3>
            <ol>
                <li><strong>Check Google Console:</strong> Verify redirect URIs match exactly</li>
                <li><strong>Check OAuth Consent Screen:</strong> Make sure app is properly configured</li>
                <li><strong>Add Test User:</strong> If in testing mode, add your email</li>
                <li><strong>Check Client ID:</strong> Verify the client ID is correct</li>
                <li><strong>Check Scopes:</strong> Ensure email, profile, and openid scopes are enabled</li>
                <li><strong>Clear Browser Cache:</strong> Clear cookies and cache</li>
                <li><strong>Check Network:</strong> Ensure localhost:3000 is accessible</li>
                <li><strong>Check Environment Variables:</strong> Verify GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET are set</li>
            </ol>
        </div>

        <div class="debug-section">
            <h3>📝 Environment Variables Check</h3>
            <div id="envCheck">
                <p>Checking environment variables...</p>
            </div>
        </div>
    </div>

    <script>
        // Base URL for API calls
        const API_BASE = 'http://localhost:3000';
        
        // Analyze current URL
        function analyzeCurrentUrl() {
            const url = window.location.href;
            const urlParams = new URLSearchParams(window.location.search);
            
            const analysis = {
                fullUrl: url,
                protocol: window.location.protocol,
                host: window.location.host,
                pathname: window.location.pathname,
                search: window.location.search,
                params: {}
            };
            
            for (let [key, value] of urlParams) {
                analysis.params[key] = value;
            }
            
            const urlAnalysisDiv = document.getElementById('urlAnalysis');
            urlAnalysisDiv.innerHTML = '<pre>' + JSON.stringify(analysis, null, 2) + '</pre>';
            
            // Check for OAuth2 callback
            if (analysis.params.access_token) {
                urlAnalysisDiv.innerHTML += '<div class="success">✅ OAuth2 access token received!</div>';
                urlAnalysisDiv.innerHTML += '<div class="info"><strong>User Data:</strong><pre>' + JSON.stringify(JSON.parse(analysis.params.user || '{}'), null, 2) + '</pre></div>';
            } else if (analysis.params.error) {
                urlAnalysisDiv.innerHTML += '<div class="error">❌ OAuth2 error: ' + analysis.params.error + '</div>';
            }
        }

        // Test OAuth2 status
        async function testOAuth2Status() {
            try {
                const response = await fetch(`${API_BASE}/api/v1/oauth2/status`);
                const data = await response.json();
                
                const configCheckDiv = document.getElementById('configCheck');
                configCheckDiv.innerHTML = '<div class="success">✅ OAuth2 Status Retrieved</div><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                const configCheckDiv = document.getElementById('configCheck');
                configCheckDiv.innerHTML = '<div class="error">❌ Error checking OAuth2 status: ' + error.message + '</div>';
                configCheckDiv.innerHTML += '<div class="info">Note: This endpoint might not exist in your NestJS app. Check if you have implemented it.</div>';
            }
        }

        // Test configuration endpoint
        async function testConfigEndpoint() {
            try {
                const response = await fetch(`${API_BASE}/api/v1/config`);
                const data = await response.json();
                
                const configCheckDiv = document.getElementById('configCheck');
                configCheckDiv.innerHTML = '<div class="success">✅ Configuration Retrieved</div><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                const configCheckDiv = document.getElementById('configCheck');
                configCheckDiv.innerHTML = '<div class="error">❌ Error checking configuration: ' + error.message + '</div>';
                configCheckDiv.innerHTML += '<div class="info">Note: This endpoint might not exist. You can implement it to expose safe configuration data.</div>';
            }
        }

        // Show debug information
        function showDebugInfo() {
            const debugInfo = {
                userAgent: navigator.userAgent,
                currentUrl: window.location.href,
                referrer: document.referrer,
                timestamp: new Date().toISOString(),
                cookies: document.cookie,
                localStorage: Object.keys(localStorage),
                sessionStorage: Object.keys(sessionStorage),
                apiBase: API_BASE
            };
            
            const debugInfoDiv = document.getElementById('debugInfo');
            debugInfoDiv.innerHTML = '<pre>' + JSON.stringify(debugInfo, null, 2) + '</pre>';
        }

        // Check for OAuth2 errors in URL
        function checkForOAuth2Errors() {
            const urlParams = new URLSearchParams(window.location.search);
            const error = urlParams.get('error');
            const errorDescription = urlParams.get('error_description');
            
            if (error) {
                const container = document.querySelector('.container');
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error';
                errorDiv.innerHTML = `
                    <h3>❌ OAuth2 Error Detected</h3>
                    <p><strong>Error:</strong> ${error}</p>
                    ${errorDescription ? `<p><strong>Description:</strong> ${errorDescription}</p>` : ''}
                    <p><strong>Possible Solutions:</strong></p>
                    <ul>
                        <li>Check Google Console redirect URI configuration</li>
                        <li>Verify OAuth consent screen settings</li>
                        <li>Add your email as a test user if in testing mode</li>
                        <li>Clear browser cookies and cache</li>
                        <li>Check your NestJS environment variables</li>
                    </ul>
                `;
                container.insertBefore(errorDiv, container.firstChild);
            }
        }

        // Generate expected Google OAuth URL
        function generateExpectedGoogleUrl() {
            const clientId = '*************-3uiik72ohsfr2ouioror3fm1jqc493os.apps.googleusercontent.com';
            const redirectUri = 'http://localhost:3000/oauth2/callback/google';
            const scope = 'email profile openid';
            const state = 'test-state-' + Date.now();
            
            const googleUrl = `https://accounts.google.com/o/oauth2/v2/auth?response_type=code&client_id=${clientId}&scope=${encodeURIComponent(scope)}&redirect_uri=${encodeURIComponent(redirectUri)}&state=${state}`;
            
            document.getElementById('expectedGoogleUrl').textContent = googleUrl;
        }

        // Check environment variables (simulated)
        function checkEnvironmentVariables() {
            const envCheckDiv = document.getElementById('envCheck');
            const envVars = [
                { name: 'GOOGLE_CLIENT_ID', status: 'warning', message: 'Check if set in your .env file' },
                { name: 'GOOGLE_CLIENT_SECRET', status: 'warning', message: 'Check if set in your .env file' },
                { name: 'GOOGLE_REDIRECT_URI', status: 'ok', message: 'Should be: {baseUrl}/oauth2/callback/{registrationId}' },
                { name: 'GOOGLE_SCOPE', status: 'ok', message: 'Should be: email,profile,openid' },
                { name: 'OAUTH2_AUTHORIZED_REDIRECT_URIS', status: 'ok', message: 'Should include your frontend redirect URIs' }
            ];
            
            let html = '<div class="config-item"><strong>Variable</strong><strong>Status</strong></div>';
            envVars.forEach(env => {
                const statusClass = env.status === 'ok' ? 'status-ok' : env.status === 'error' ? 'status-error' : 'status-warning';
                html += `
                    <div class="config-item">
                        <span>${env.name}</span>
                        <span><span class="status-indicator ${statusClass}"></span>${env.message}</span>
                    </div>
                `;
            });
            
            envCheckDiv.innerHTML = html;
        }

        // Initialize debug page
        window.onload = function() {
            analyzeCurrentUrl();
            checkForOAuth2Errors();
            generateExpectedGoogleUrl();
            checkEnvironmentVariables();
            
            // Try to test OAuth2 status on load
            setTimeout(() => {
                testOAuth2Status();
            }, 1000);
        };
    </script>
</body>
</html> 