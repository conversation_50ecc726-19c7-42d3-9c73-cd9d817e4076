PORT=3000
NODE_ENV=development
DB_HOST=localhost
DB_PORT=5433
DB_USER=postgres
DB_PASSWORD=password
DB_NAME=userauth
JWT_SECRET=supersecretkey
JWT_EXPIRATION=86400000
SESSION_SECRET=dev-session-secret-change-in-production
# Email SMTP Settings
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=fqactehafmzlltzz
SMTP_FROM='"No Reply" <<EMAIL>>'
APP_URL=http://localhost:3000
API_URL=http://localhost:3000
#Cors Configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,PATCH,OPTIONS
CORS_ALLOW_CREDENTIALS=true
CORS_MAX_AGE=3600
# OAuth2 Authorized Redirect URIs (comma-separated)
# These are the URIs where users will be redirected after successful OAuth
OAUTH2_AUTHORIZED_REDIRECT_URIS=http://localhost:3000/oauth2/callback/google, http://localhost:3000/oauth2/redirect, myandroidapp://oauth2/redirect, myiosapp://oauth2/redirect
# Google OAuth Configuration
# Get these from Google Cloud Console: https://console.cloud.google.com/
GOOGLE_CLIENT_ID=1073981864538-********************************.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_REDIRECT_URI=http://localhost:3000/oauth2/callback/google
GOOGLE_SCOPE=email,profile,openid
NODE_TLS_REJECT_UNAUTHORIZED=0