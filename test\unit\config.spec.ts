import { ConfigModule } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { corsConfig } from '../../src/config/cors.config';
import { emailConfig } from '../../src/config/email.config';
import { databaseConfig } from '../../src/config/database.config';
import googleOAuthConfig from '../../src/config/google-oauth.config';

describe('Configuration', () => {
  let configService: ConfigService;

  beforeEach(async () => {
    // Set test environment variables
    process.env.NODE_ENV = 'test';
    process.env.GOOGLE_CLIENT_ID = 'dummy-google-client-id';
    process.env.GOOGLE_CLIENT_SECRET = 'dummy-google-client-secret';
    process.env.GOOGLE_REDIRECT_URI = 'http://localhost:3000/oauth2/callback/google';
    process.env.SMTP_HOST = 'smtp.test.com';
    process.env.SMTP_PORT = '587';
    process.env.SMTP_USER = '<EMAIL>';
    process.env.SMTP_PASS = 'testpassword';
    process.env.SMTP_FROM = '<EMAIL>';
    process.env.APP_URL = 'http://localhost:3000';
    
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env.test',
          load: [databaseConfig, emailConfig, corsConfig, googleOAuthConfig],
        }),
      ],
    }).compile();

    configService = module.get<ConfigService>(ConfigService);
  });

  describe('Google OAuth Configuration', () => {
    it('should load Google OAuth configuration correctly', () => {
      const googleConfig = configService.get('googleOAuth');
      
      expect(googleConfig).toBeDefined();
      expect(googleConfig.clientId).toBe('dummy-google-client-id');
      expect(googleConfig.clientSecret).toBe('dummy-google-client-secret');
      expect(googleConfig.redirectUri).toBe('http://localhost:3000/oauth2/callback/google');
      expect(googleConfig.scope).toBe('email,profile,openid');
      // Check that the array contains the expected URIs without caring about order
      expect(googleConfig.authorizedRedirectUris).toContain('http://localhost:3000/oauth2/callback/google');
      expect(googleConfig.authorizedRedirectUris).toContain('http://localhost:3000/chidhagni/oauth2/redirect');
      expect(googleConfig.authorizedRedirectUris).toContain('myandroidapp://oauth2/redirect');
      expect(googleConfig.authorizedRedirectUris).toContain('myiosapp://oauth2/redirect');
    });

    it('should handle redirect URI placeholders correctly', () => {
      const googleConfig = configService.get('googleOAuth');
      
      // Should replace {baseUrl} with APP_URL and {registrationId} with 'google'
      expect(googleConfig.redirectUri).toBe('http://localhost:3000/oauth2/callback/google');
    });
  });

  describe('CORS Configuration', () => {
    it('should load CORS configuration correctly', () => {
      const corsConfigResult = configService.get('cors');
      
      expect(corsConfigResult).toBeDefined();
      expect(corsConfigResult.allowedOrigins).toContain('http://localhost:3000');
      expect(corsConfigResult.allowedOrigins).toContain('http://localhost:3001');
      expect(corsConfigResult.allowedMethods).toContain('GET');
      expect(corsConfigResult.allowedMethods).toContain('POST');
      expect(corsConfigResult.allowCredentials).toBe(true);
      expect(corsConfigResult.maxAge).toBe(3600);
    });
  });

  describe('Email Configuration', () => {
    it('should load email configuration correctly', () => {
      const emailConfigResult = configService.get('email');
      
      expect(emailConfigResult).toBeDefined();
      expect(emailConfigResult.host).toBe('smtp.test.com');
      expect(emailConfigResult.port).toBe(587);
      expect(emailConfigResult.user).toBe('<EMAIL>');
      expect(emailConfigResult.pass).toBe('testpassword');
      expect(emailConfigResult.from).toBe('<EMAIL>');
    });
  });

  describe('Database Configuration', () => {
    it('should load database configuration correctly', () => {
      const dbConfig = configService.get('database');
      
      expect(dbConfig).toBeDefined();
      expect(dbConfig.host).toBe('localhost');
      expect(dbConfig.port).toBe(5433);
      expect(dbConfig.user).toBe('postgres');
      expect(dbConfig.password).toBe('password');
      expect(dbConfig.database).toBe('userauth');
    });
  });
}); 