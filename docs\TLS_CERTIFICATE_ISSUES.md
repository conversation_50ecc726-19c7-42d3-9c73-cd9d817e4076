# TLS Certificate Issues and Solutions

## Problem Description

The error `self-signed certificate in certificate chain` occurs when Node.js applications try to establish secure connections (HTTPS/TLS) with servers that have certificate chain issues. This commonly happens with:

1. **SMTP connections** (Gmail, Outlook, etc.)
2. **Database connections** (PostgreSQL with SSL)
3. **API calls** to external services
4. **Corporate firewalls** that intercept SSL traffic

## Root Causes

### 1. Self-Signed Certificates
- Development servers often use self-signed certificates
- Corporate networks may use internal certificate authorities
- Some SMTP providers have certificate chain issues

### 2. Certificate Chain Problems
- Missing intermediate certificates
- Expired certificates
- Incorrect certificate order in chain

### 3. Network Interference
- Corporate firewalls/proxies
- Antivirus software intercepting SSL
- VPN connections

## Solutions Implemented

### 1. TLS Configuration Utility (`src/common/security/tls.config.ts`)

This utility provides centralized TLS configuration and supports multiple environment names:

```typescript
// Automatically configures TLS for development environments
TlsConfig.configureForDevelopment();

// Get SMTP-specific TLS options
const smtpTlsOptions = TlsConfig.getSmtpTlsOptions();

// Get database-specific TLS options
const dbTlsOptions = TlsConfig.getDatabaseTlsOptions();
```

**Supported Environment Names:**
- `development` / `dev` / `local` / `test` → Certificate verification disabled
- `production` → Full certificate verification enabled

### 2. Email Module Updates

The email module now uses proper TLS configuration:

```typescript
// In src/email/email.module.ts
tls: TlsConfig.getSmtpTlsOptions(),
```

### 3. Database Configuration

Database connections include TLS options:

```typescript
// In src/database/knexfile.ts
ssl: getDatabaseTlsOptions(),
```

### 4. Environment-Based Configuration

The application automatically detects the environment and applies appropriate settings:

- **Development-like environments** (`dev`, `development`, `local`, `test`): Certificate verification disabled
- **Production**: Full certificate verification enabled

## Environment Variables

### For Development (Optional)
```bash
# Disable certificate verification (development only)
NODE_TLS_REJECT_UNAUTHORIZED=0
```

### For Production (Recommended)
```bash
# Ensure certificate verification is enabled
NODE_TLS_REJECT_UNAUTHORIZED=1
```

## Testing TLS Connections

Use the provided test scripts to diagnose TLS issues:

```bash
# Test TLS connections
node scripts/test-tls.js

# Test environment-specific TLS configuration
node scripts/test-env-tls.js
```

These scripts test:
- Gmail SMTP connections
- Google API connections
- Local development server
- Different environment configurations

## Security Considerations

### Development Environment
- Certificate verification is disabled for convenience
- This is safe for local development only
- Never use this setting in production

### Production Environment
- Full certificate verification is enforced
- All TLS connections must use valid certificates
- Self-signed certificates are rejected

## Troubleshooting

### 1. Gmail SMTP Issues
```bash
# Check if Gmail SMTP is accessible
telnet smtp.gmail.com 587
```

### 2. Database SSL Issues
```bash
# Test PostgreSQL SSL connection
psql "postgresql://user:pass@host:port/db?sslmode=require"
```

### 3. Network Issues
```bash
# Check if corporate firewall is blocking
curl -v https://smtp.gmail.com:587
```

### 4. Environment-Specific Issues
```bash
# Test your specific environment
NODE_ENV=dev node scripts/test-env-tls.js
```

## Best Practices

### 1. Development
- Use the TLS configuration utility
- Test with the provided test scripts
- Monitor logs for certificate warnings

### 2. Production
- Always use valid SSL certificates
- Never disable certificate verification
- Monitor certificate expiration dates

### 3. Monitoring
- Log TLS connection attempts
- Monitor certificate verification failures
- Set up alerts for certificate issues

## Common Error Messages

| Error | Cause | Solution |
|-------|-------|----------|
| `self-signed certificate in certificate chain` | Invalid certificate chain | Use TLS configuration utility |
| `UNABLE_TO_VERIFY_LEAF_SIGNATURE` | Missing intermediate certificates | Update certificate chain |
| `CERTIFICATE_VERIFY_FAILED` | Expired or invalid certificate | Renew certificate |
| `ECONNRESET` | Network/firewall blocking | Check network configuration |

## Environment Support

The application now supports multiple environment names:

| Environment | Certificate Verification | Use Case |
|-------------|-------------------------|----------|
| `development` | Disabled | Local development |
| `dev` | Disabled | Development server |
| `local` | Disabled | Local testing |
| `test` | Disabled | Automated testing |
| `production` | Enabled | Production deployment |

## Additional Resources

- [Node.js TLS Documentation](https://nodejs.org/api/tls.html)
- [Gmail SMTP Settings](https://support.google.com/mail/answer/7126229)
- [PostgreSQL SSL Configuration](https://www.postgresql.org/docs/current/ssl-tcp.html) 