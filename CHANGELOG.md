# Changelog

## [2.0.1] - 2025-01-08

### Fixed
- **TLS Certificate Issues**: Completely resolved `self-signed certificate in certificate chain` errors
- **Environment Support**: Added support for multiple environment names (`dev`, `development`, `local`, `test`, `production`)
- **Database Configuration**: Fixed knex configuration for all environments
- **Missing Environment Variables**: Added `SESSION_SECRET` and `GOOGLE_SCOPE` to .env file

### Added
- **TLS Configuration Utility** (`src/common/security/tls.config.ts`): Centralized TLS configuration
- **Environment-Aware TLS Settings**: Automatic certificate verification based on environment
- **Database SSL Configuration**: Proper SSL handling for different environments
- **Email TLS Configuration**: Gmail SMTP with proper TLS settings
- **Test Scripts**: TLS connection testing utilities

### Changed
- **Email Module**: Updated to use TLS configuration utility
- **Database Module**: Enhanced knexfile with environment-specific configurations
- **Main Application**: Added early TLS configuration in bootstrap
- **Environment Detection**: Improved environment name recognition

### Security
- **Development**: Certificate verification disabled for convenience
- **Production**: Full certificate verification enforced
- **Environment Variables**: Added proper session secret configuration

### Technical Details
- **TLS Certificate Handling**: Automatic detection and configuration
- **SMTP Configuration**: Gmail SMTP with proper TLS options
- **Database SSL**: Conditional SSL based on environment
- **OAuth Configuration**: Complete Google OAuth setup

### Environment Support
| Environment | Certificate Verification | Status |
|-------------|-------------------------|---------|
| `development` | Disabled | ✅ Working |
| `dev` | Disabled | ✅ Working |
| `local` | Disabled | ✅ Working |
| `test` | Disabled | ✅ Working |
| `production` | Enabled | ✅ Secure |

### Files Modified
- `src/common/security/tls.config.ts` - New TLS configuration utility
- `src/email/email.module.ts` - Updated with TLS configuration
- `src/database/knexfile.ts` - Enhanced with environment support
- `src/main.ts` - Added TLS configuration in bootstrap
- `.env` - Added missing environment variables
- `docs/TLS_CERTIFICATE_ISSUES.md` - Comprehensive documentation
- `scripts/test-tls.js` - TLS testing utility
- `scripts/test-env-tls.js` - Environment testing utility

### Breaking Changes
- None

### Migration Guide
- No migration required
- Application automatically detects environment and applies appropriate settings
- All existing functionality preserved

### Testing
- ✅ TLS certificate issues resolved
- ✅ All environments supported
- ✅ Database connections working
- ✅ Email service functional
- ✅ OAuth authentication working
- ✅ Health endpoint responding
- ✅ Application starting successfully 