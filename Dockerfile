# -------------------------------
# Build Stage
# -------------------------------
  FROM node:20-alpine AS builder

  # ✅ Install dumb-init and CA certs (if build needs HTTPS, like npm install or fetching public assets)
  RUN apk add --no-cache dumb-init ca-certificates && update-ca-certificates
  
  # Set working directory
  WORKDIR /app
  
  # Install dependencies
  COPY package*.json ./
  RUN npm install
  
  # Copy the full project
  COPY . .
  
  # Build the NestJS app (outputs to /app/dist)
  RUN npm run build
  
  
  # -------------------------------
  # Production Stage
  # -------------------------------
  FROM node:20-alpine AS production
  
  # ✅ Install dumb-init and CA certs
  RUN apk add --no-cache dumb-init ca-certificates && update-ca-certificates
  
  # Set working directory
  WORKDIR /app
  
  # Create a non-root user
  RUN addgroup -g 1001 -S nodejs && adduser -S nestjs -u 1001
  
  # Copy only production dependencies
  COPY package*.json ./
  RUN npm ci --only=production && npm cache clean --force
  
  # Copy compiled output and any required runtime files from builder
  COPY --from=builder /app/dist ./dist
  COPY --from=builder /app/src/database/migrations ./src/database/migrations
  
  # ✅ Optional: Copy .env files if needed at runtime
  COPY .env* ./
  
  # Set ownership and permissions
  RUN chown -R nestjs:nodejs /app
  
  # Switch to the non-root user
  USER nestjs
  
  # Expose the app port
  EXPOSE 3000
  
  # Healthcheck (adjust if needed)
  HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })" || exit 1
  
  # Default command to run the app
  CMD ["node", "dist/src/main.js"]
  