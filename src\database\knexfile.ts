import type { Knex } from 'knex';
import * as dotenv from 'dotenv';
import * as path from 'path';

const envFile = process.env.NODE_ENV === 'development'
  ? path.resolve(__dirname, '../../.env.development')
  : path.resolve(__dirname, '../../.env');

dotenv.config({ path: envFile });

// Check if we're in a development-like environment
const isDevelopmentEnvironment = () => {
  const env = process.env.NODE_ENV?.toLowerCase() || '';
  return env === 'development' || env === 'dev' || env === 'local' || env === 'test';
};

// TLS configuration for database connections
const getDatabaseTlsOptions = () => {
  const isDevelopment = isDevelopmentEnvironment();
  const isProduction = process.env.NODE_ENV?.toLowerCase() === 'production';
  
  // In development, try SSL but don't require it
  if (isDevelopment) {
    return {
      rejectUnauthorized: false,
      // Allow fallback to non-SSL if server doesn't support it
    };
  }
  
  // In production, require SSL
  if (isProduction) {
    return {
      rejectUnauthorized: true,
    };
  }
  
  // Default: no SSL
  return false;
};

const config: { [key: string]: Knex.Config } = {
  
  development: {
    client: 'pg',
    connection: {
      host: process.env.DB_HOST,
      port: Number(process.env.DB_PORT ?? 5432),
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      // Only enable SSL if explicitly configured
      ...(process.env.DB_SSL === 'true' && {
        ssl: getDatabaseTlsOptions(),
      }),
    },
    migrations: {
      directory: __dirname + '/migrations',
      extension: 'ts',
    },
    pool: { min: 2, max: 10 },
    debug: true,
  },

  // Add configuration for other environments
  dev: {
    client: 'pg',
    connection: {
      host: process.env.DB_HOST,
      port: Number(process.env.DB_PORT ?? 5432),
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      // Only enable SSL if explicitly configured
      ...(process.env.DB_SSL === 'true' && {
        ssl: getDatabaseTlsOptions(),
      }),
    },
    migrations: {
      directory: __dirname + '/migrations',
      extension: 'ts',
    },
    pool: { min: 2, max: 10 },
    debug: true,
  },

  test: {
    client: 'pg',
    connection: {
      host: process.env.DB_HOST,
      port: Number(process.env.DB_PORT ?? 5432),
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      // Only enable SSL if explicitly configured
      ...(process.env.DB_SSL === 'true' && {
        ssl: getDatabaseTlsOptions(),
      }),
    },
    migrations: {
      directory: __dirname + '/migrations',
      extension: 'ts',
    },
    pool: { min: 2, max: 10 },
    debug: true,
  },

  production: {
    client: 'pg',
    connection: {
      host: process.env.DB_HOST,
      port: Number(process.env.DB_PORT ?? 5432),
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      ssl: getDatabaseTlsOptions(),
    },
    migrations: {
      directory: __dirname + '/migrations',
      extension: 'ts',
    },
    pool: { min: 2, max: 10 },
    debug: false,
  },
};

export default config; 