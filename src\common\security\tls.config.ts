import { Logger } from '@nestjs/common';

/**
 * TLS Configuration utility for handling certificate issues in development
 */
export class TlsConfig {
  private static readonly logger = new Logger(TlsConfig.name);

  /**
   * Check if we're in a development-like environment
   */
  private static isDevelopmentEnvironment(): boolean {
    const env = process.env.NODE_ENV?.toLowerCase() || '';
    return env === 'development' || env === 'dev' || env === 'local' || env === 'test';
  }

  /**
   * Configure TLS settings for development environment
   * This helps resolve self-signed certificate issues
   */
  static configureForDevelopment(): void {
    if (this.isDevelopmentEnvironment()) {
      this.logger.warn(`Configuring TLS for ${process.env.NODE_ENV} environment - certificate verification disabled`);
      
      // Disable certificate verification for development
      process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
      
      // Set additional TLS options for development
      process.env.NODE_OPTIONS = process.env.NODE_OPTIONS 
        ? `${process.env.NODE_OPTIONS} --tls-min-v1.0`
        : '--tls-min-v1.0';
    }
  }

  /**
   * Get TLS options for SMTP connections
   */
  static getSmtpTlsOptions(): any {
    const isDevelopment = this.isDevelopmentEnvironment();
    
    return {
      rejectUnauthorized: !isDevelopment,
      ciphers: 'SSLv3',
      ...(isDevelopment && {
        ignoreTLS: false,
        requireTLS: false,
      }),
    };
  }

  /**
   * Get TLS options for database connections
   */
  static getDatabaseTlsOptions(): any {
    const isDevelopment = this.isDevelopmentEnvironment();
    
    return {
      rejectUnauthorized: !isDevelopment,
      ...(isDevelopment && {
        ssl: {
          rejectUnauthorized: false,
        },
      }),
    };
  }

  /**
   * Check if TLS certificate verification is disabled
   */
  static isCertificateVerificationDisabled(): boolean {
    return process.env.NODE_TLS_REJECT_UNAUTHORIZED === '0';
  }

  /**
   * Log TLS configuration status
   */
  static logConfigurationStatus(): void {
    const isDisabled = this.isCertificateVerificationDisabled();
    const env = process.env.NODE_ENV || 'unknown';
    
    this.logger.log(`TLS Configuration - Environment: ${env}, Certificate Verification: ${isDisabled ? 'DISABLED' : 'ENABLED'}`);
    
    if (isDisabled && env === 'production') {
      this.logger.error('WARNING: Certificate verification is disabled in production environment!');
    }
  }
} 