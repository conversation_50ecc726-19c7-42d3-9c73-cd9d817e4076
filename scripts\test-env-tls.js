#!/usr/bin/env node

/**
 * Environment TLS Test Script
 * Tests TLS configuration in different environments
 */

// Mock the TlsConfig for testing since we can't import TypeScript directly
const TlsConfig = {
  configureForDevelopment: () => {
    const env = process.env.NODE_ENV?.toLowerCase() || '';
    const isDev = env === 'development' || env === 'dev' || env === 'local' || env === 'test';
    
    if (isDev) {
      console.log(`Configuring TLS for ${env} environment - certificate verification disabled`);
      process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
    }
  },
  
  logConfigurationStatus: () => {
    const isDisabled = process.env.NODE_TLS_REJECT_UNAUTHORIZED === '0';
    const env = process.env.NODE_ENV || 'unknown';
    console.log(`TLS Configuration - Environment: ${env}, Certificate Verification: ${isDisabled ? 'DISABLED' : 'ENABLED'}`);
  },
  
  getSmtpTlsOptions: () => {
    const env = process.env.NODE_ENV?.toLowerCase() || '';
    const isDev = env === 'development' || env === 'dev' || env === 'local' || env === 'test';
    
    return {
      rejectUnauthorized: !isDev,
      ciphers: 'SSLv3',
      ...(isDev && {
        ignoreTLS: false,
        requireTLS: false,
      }),
    };
  },
  
  getDatabaseTlsOptions: () => {
    const env = process.env.NODE_ENV?.toLowerCase() || '';
    const isDev = env === 'development' || env === 'dev' || env === 'local' || env === 'test';
    
    return {
      rejectUnauthorized: !isDev,
      ...(isDev && {
        ssl: {
          rejectUnauthorized: false,
        },
      }),
    };
  },
  
  isCertificateVerificationDisabled: () => {
    return process.env.NODE_TLS_REJECT_UNAUTHORIZED === '0';
  }
};

function testEnvironment(envName) {
  console.log(`\n🧪 Testing environment: ${envName}`);
  
  // Set environment
  process.env.NODE_ENV = envName;
  
  // Configure TLS
  TlsConfig.configureForDevelopment();
  TlsConfig.logConfigurationStatus();
  
  // Test SMTP TLS options
  const smtpOptions = TlsConfig.getSmtpTlsOptions();
  console.log(`SMTP TLS Options:`, smtpOptions);
  
  // Test Database TLS options
  const dbOptions = TlsConfig.getDatabaseTlsOptions();
  console.log(`Database TLS Options:`, dbOptions);
  
  // Check certificate verification status
  const isDisabled = TlsConfig.isCertificateVerificationDisabled();
  console.log(`Certificate Verification Disabled: ${isDisabled}`);
  
  console.log(`✅ Environment ${envName} test completed`);
}

async function runEnvironmentTests() {
  console.log('🔍 Testing TLS Configuration in Different Environments');
  console.log('==================================================');
  
  const environments = ['development', 'dev', 'local', 'test', 'production'];
  
  for (const env of environments) {
    testEnvironment(env);
  }
  
  console.log('\n📋 Environment Test Summary:');
  console.log('- development, dev, local, test: Certificate verification should be DISABLED');
  console.log('- production: Certificate verification should be ENABLED');
  console.log('- All non-production environments should handle self-signed certificates');
}

// Run tests if this script is executed directly
if (require.main === module) {
  runEnvironmentTests().catch((error) => {
    console.error(`Test suite failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { testEnvironment }; 