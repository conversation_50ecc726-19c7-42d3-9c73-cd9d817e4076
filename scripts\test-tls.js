#!/usr/bin/env node

/**
 * TLS Test Script
 * This script helps diagnose TLS certificate issues
 */

const https = require('https');
const tls = require('tls');
const { Logger } = require('@nestjs/common');

const logger = new Logger('TlsTest');

function testTlsConnection(host, port = 443) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: host,
      port: port,
      method: 'GET',
      path: '/',
      rejectUnauthorized: false, // Allow self-signed certificates for testing
    };

    const req = https.request(options, (res) => {
      logger.log(`✅ TLS connection to ${host}:${port} successful`);
      logger.log(`Status: ${res.statusCode}`);
      logger.log(`Headers: ${JSON.stringify(res.headers, null, 2)}`);
      resolve({ success: true, statusCode: res.statusCode });
    });

    req.on('error', (error) => {
      logger.error(`❌ TLS connection to ${host}:${port} failed`);
      logger.error(`Error: ${error.message}`);
      reject(error);
    });

    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Connection timeout'));
    });

    req.end();
  });
}

function testSmtpConnection(host, port = 587) {
  return new Promise((resolve, reject) => {
    const socket = tls.connect({
      host: host,
      port: port,
      rejectUnauthorized: false,
    }, () => {
      logger.log(`✅ SMTP TLS connection to ${host}:${port} successful`);
      logger.log(`Protocol: ${socket.getProtocol()}`);
      logger.log(`Cipher: ${socket.getCipher()}`);
      socket.end();
      resolve({ success: true });
    });

    socket.on('error', (error) => {
      logger.error(`❌ SMTP TLS connection to ${host}:${port} failed`);
      logger.error(`Error: ${error.message}`);
      reject(error);
    });

    socket.setTimeout(10000, () => {
      socket.destroy();
      reject(new Error('Connection timeout'));
    });
  });
}

async function runTests() {
  logger.log('🔍 Starting TLS connection tests...');
  logger.log('');

  // Test Gmail SMTP
  try {
    await testSmtpConnection('smtp.gmail.com', 587);
  } catch (error) {
    logger.error(`Failed to connect to Gmail SMTP: ${error.message}`);
  }

  logger.log('');

  // Test Google APIs
  try {
    await testTlsConnection('www.googleapis.com');
  } catch (error) {
    logger.error(`Failed to connect to Google APIs: ${error.message}`);
  }

  logger.log('');

  // Test localhost (if running)
  try {
    await testTlsConnection('localhost', 3000);
  } catch (error) {
    logger.log('Localhost test skipped (server may not be running)');
  }

  logger.log('');
  logger.log('📋 TLS Test Summary:');
  logger.log('- If Gmail SMTP test failed, check your network/firewall settings');
  logger.log('- If Google APIs test failed, check your internet connection');
  logger.log('- The application should now handle certificate issues automatically');
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch((error) => {
    logger.error(`Test suite failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { testTlsConnection, testSmtpConnection }; 